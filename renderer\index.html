<!doctype html>
<html lang="zh-CN">
<head>
  <meta charset="utf-8" />
  <meta http-equiv="X-UA-Compatible" content="IE=edge" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>MyList</title>
  <link rel="stylesheet" href="./style.css" />
</head>
<body>
  <div class="panel">
    <!-- 主页面 -->
    <div id="mainPage" class="page">
      <header class="panel-header">
        <h1 class="title">MyList</h1>
        <button class="btn btn-ghost" id="btnClose" title="关闭">×</button>
      </header>

      <section class="card add-area">
        <div class="input-row">
          <input id="newItemInput" class="input" type="text" placeholder="新增事项..." />
          <input id="timeInput" class="input" type="datetime-local" />
        </div>
        <button id="addBtn" class="btn btn-primary">添加</button>
      </section>

      <section id="listPreview" class="list-preview card" onclick="showListPage()">
        <div class="preview-header">
          <span id="itemCount" class="item-count">0 个事项</span>
          <span class="expand-icon">▶</span>
        </div>
        <div id="previewItems" class="preview-items"></div>
        <div class="click-hint">点击查看所有事项</div>
      </section>
    </div>

    <!-- 列表页面 -->
    <div id="listPage" class="page hidden">
      <header class="panel-header">
        <button class="btn btn-ghost" id="btnBack" title="返回">←</button>
        <h1 class="title">所有事项</h1>
        <button class="btn btn-ghost" id="btnClose2" title="关闭">×</button>
      </header>

      <section id="fullList" class="full-list card"></section>

      <footer class="list-footer">
        <button id="clearDoneBtn" class="btn btn-secondary">清除已完成</button>
      </footer>
    </div>
  </div>

  <script src="./index.js"></script>
</body>
</html>


