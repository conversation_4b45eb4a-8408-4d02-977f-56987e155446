const { app, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tray, <PERSON>u, nativeImage, screen, ipcMain, Notification } = require('electron');
const path = require('path');
const fs = require('fs');

let tray = null;
let panelWindow = null;
let saveTimer = null;
let reminderTimers = new Map();

const userDataDir = app.getPath('userData');
const dataFile = path.join(userDataDir, 'mylist-data.json');

function readJSONSafe(filePath, fallback) {
  try {
    if (fs.existsSync(filePath)) {
      const raw = fs.readFileSync(filePath, 'utf-8');
      return JSON.parse(raw);
    }
  } catch {}
  return fallback;
}

function writeJSONSafe(filePath, data) {
  try {
    fs.mkdirSync(path.dirname(filePath), { recursive: true });
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');
  } catch {}
}

function loadData() {
  const fallback = { items: [], window: {} };
  return readJSONSafe(dataFile, fallback);
}

function saveDataDebounced(nextData) {
  if (saveTimer) clearTimeout(saveTimer);
  saveTimer = setTimeout(() => writeJSONSafe(dataFile, nextData), 100);
}

function getDefaultPanelPosition(panelSize) {
  const primaryDisplay = screen.getPrimaryDisplay();
  const workArea = primaryDisplay.workArea;
  // Windows 任务栏通常在底部；我们将窗口贴近右下角任务栏上方
  const x = Math.round(workArea.x + workArea.width - panelSize.width - 8);
  const y = Math.round(workArea.y + workArea.height - panelSize.height - 8);
  return { x, y };
}

function createPanelWindow() {
  if (panelWindow && !panelWindow.isDestroyed()) return panelWindow;

  panelWindow = new BrowserWindow({
    width: 380,
    height: 520,
    frame: false,
    show: false,
    resizable: false,
    movable: true,
    skipTaskbar: true,
    alwaysOnTop: true,
    transparent: true,
    hasShadow: false,
    webPreferences: {
      preload: path.join(__dirname, 'preload.js'),
    },
  });

  panelWindow.removeMenu();
  panelWindow.loadFile(path.join(__dirname, 'renderer', 'index.html'));

  panelWindow.on('blur', () => {
    if (panelWindow && panelWindow.isVisible()) {
      panelWindow.hide();
    }
  });

  // 记忆窗口位置
  const persistBounds = () => {
    const data = loadData();
    if (panelWindow) {
      const b = panelWindow.getBounds();
      data.window = { x: b.x, y: b.y, width: b.width, height: b.height };
      saveDataDebounced(data);
    }
  };
  panelWindow.on('moved', persistBounds);
  panelWindow.on('resized', persistBounds);

  return panelWindow;
}

function togglePanel() {
  const win = createPanelWindow();
  const data = loadData();
  const currentBounds = win.getBounds();
  const target = data.window && typeof data.window.x === 'number'
    ? { x: data.window.x, y: data.window.y }
    : getDefaultPanelPosition({ width: currentBounds.width, height: currentBounds.height });
  win.setPosition(target.x, target.y, false);
  if (win.isVisible()) {
    win.hide();
  } else {
    win.showInactive();
    win.focus();
  }
}

function createTray() {
  const iconPath = path.join(__dirname, 'renderer', 'icons', 'trayTemplate.png');

  // 创建简单可靠的托盘图标
  const createFallbackTrayImage = () => {
    const size = 16;
    const totalBytes = size * size * 4; // RGBA
    const data = Buffer.alloc(totalBytes, 0);
    
    // 绘制红色背景和白色图案
    for (let y = 0; y < size; y++) {
      for (let x = 0; x < size; x++) {
        const idx = (y * size + x) * 4;
        
        // 背景：红色 #F22D40
        if (x >= 1 && x <= 14 && y >= 1 && y <= 14) {
          data[idx + 0] = 242; // R
          data[idx + 1] = 45;  // G
          data[idx + 2] = 64;  // B
          data[idx + 3] = 255; // A
          
          // 白色圆点和横线
          if ((x === 4 && (y === 4 || y === 8 || y === 12)) || // 圆点
              (y === 4 && x >= 7 && x <= 12) ||                // 横线1
              (y === 8 && x >= 7 && x <= 11) ||                // 横线2
              (y === 12 && x >= 7 && x <= 10)) {               // 横线3
            data[idx + 0] = 255; // R
            data[idx + 1] = 255; // G
            data[idx + 2] = 255; // B
            data[idx + 3] = 230; // A
          }
        }
      }
    }
    
    return nativeImage.createFromBitmap(data, { width: size, height: size });
  };

  let trayImage = null;
  try {
    if (fs.existsSync(iconPath)) {
      const img = nativeImage.createFromPath(iconPath);
      if (!img.isEmpty()) {
        trayImage = img;
      }
    }
  } catch {}

  if (!trayImage) trayImage = createFallbackTrayImage();

  tray = new Tray(trayImage);

  const contextMenu = Menu.buildFromTemplate([
    { label: '打开/隐藏', click: () => togglePanel() },
    { type: 'separator' },
    { label: '退出', click: () => app.quit() },
  ]);

  tray.setToolTip('MyList');
  tray.setContextMenu(contextMenu);
  tray.on('click', togglePanel);
  tray.on('right-click', () => tray.popUpContextMenu());

  // 点击气泡时显示面板
  tray.on('balloon-click', () => {
    const win = createPanelWindow();
    if (!win.isVisible()) togglePanel();
  });
}

app.whenReady().then(() => {
  // Windows 通知需要 AppUserModelId
  if (process.platform === 'win32') {
    app.setAppUserModelId('com.mylist.app');
  }
  createTray();
  createPanelWindow();

  // 启动时加载数据并安排提醒
  const data = loadData();
  scheduleAllReminders(data.items || []);

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createPanelWindow();
    }
  });
});

// 保持常驻（即使所有窗口隐藏/关闭）
app.on('window-all-closed', () => {
  // 什么都不做，保持应用在托盘中运行
});

ipcMain.handle('toggle-panel', () => togglePanel());

// IPC: 数据读写
ipcMain.handle('items:get', () => {
  const data = loadData();
  return data.items || [];
});

ipcMain.handle('items:save', (_evt, items) => {
  const data = loadData();
  data.items = Array.isArray(items) ? items : [];
  writeJSONSafe(dataFile, data);
  scheduleAllReminders(data.items);
});

function clearAllReminderTimers() {
  for (const t of reminderTimers.values()) clearTimeout(t);
  reminderTimers.clear();
}

function scheduleAllReminders(items) {
  clearAllReminderTimers();
  const now = Date.now();
  for (const it of items) {
    if (!it || !it.id) continue;
    const ts = Number(it.remindAt || 0);
    const isDone = it.status === 'done';
    if (isNaN(ts) || ts <= now || isDone) continue;
    const delay = Math.min(ts - now, 2147483647); // 最长 24.8 天
    const timer = setTimeout(() => {
      showReminder(it);
      reminderTimers.delete(it.id);
    }, delay);
    reminderTimers.set(it.id, timer);
  }
}

function showReminder(item) {
  const title = '事项提醒';
  const content = item && item.title ? item.title : '有新的提醒';
  if (process.platform === 'win32' && tray && tray.displayBalloon) {
    try {
      tray.displayBalloon({ title, content });
    } catch {}
  } else {
    try {
      new Notification({ title, body: content }).show();
    } catch {}
  }
  // 弹出面板
  const win = createPanelWindow();
  if (!win.isVisible()) togglePanel();
}


