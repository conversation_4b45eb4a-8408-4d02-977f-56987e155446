const STORAGE_KEY = 'mylist-items'; // 兼容老数据：首次会迁移至主进程保存

function formatTime(timestamp) {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000);
  const itemDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
  
  const timeStr = date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  
  if (itemDate.getTime() === today.getTime()) {
    return `今天 ${timeStr}`;
  } else if (itemDate.getTime() === tomorrow.getTime()) {
    return `明天 ${timeStr}`;
  } else {
    return date.toLocaleString('zh-CN', { 
      month: 'numeric', 
      day: 'numeric', 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  }
}

function isOverdue(timestamp) {
  if (!timestamp) return false;
  return Date.now() > timestamp;
}

function saveItems(items) {
  if (window.mylistAPI && window.mylistAPI.items) {
    window.mylistAPI.items.save(items);
  } else {
    localStorage.setItem(STORAGE_KEY, JSON.stringify(items));
  }
}

function renderPreview(items) {
  const itemCount = document.getElementById('itemCount');
  const previewItems = document.getElementById('previewItems');
  
  itemCount.textContent = `${items.length} 个事项`;
  
  const fragment = document.createDocumentFragment();
  const maxPreview = 3;
  
  items.slice(0, maxPreview).forEach((it) => {
    const row = document.createElement('div');
    row.className = 'preview-item';
    
    const status = document.createElement('span');
    status.className = `status ${it.status}`;
    
    const title = document.createElement('span');
    title.textContent = it.title;
    if (it.status === 'done') title.style.textDecoration = 'line-through';
    
    row.appendChild(status);
    row.appendChild(title);
    fragment.appendChild(row);
  });
  
  if (items.length > maxPreview) {
    const more = document.createElement('div');
    more.className = 'preview-item more';
    more.textContent = `还有 ${items.length - maxPreview} 个事项...`;
    fragment.appendChild(more);
  }
  
  previewItems.innerHTML = '';
  previewItems.appendChild(fragment);
}

function renderFullList(items) {
  const list = document.getElementById('fullList');
  // 使用 DocumentFragment 减少重排
  const fragment = document.createDocumentFragment();
  
  items.forEach((it, idx) => {
    const row = document.createElement('div');
    const overdue = isOverdue(it.remindAt) && it.status !== 'done';
    row.className = `item ${overdue ? 'overdue' : ''}`;
    
    const status = document.createElement('span');
    status.className = `status ${it.status}`;
    
    const content = document.createElement('div');
    content.className = 'item-content';
    
    const title = document.createElement('div');
    title.className = `item-title ${it.status === 'done' ? 'done' : ''}`;
    title.textContent = it.title;
    
    content.appendChild(title);
    
    if (it.remindAt) {
      const time = document.createElement('div');
      time.className = 'item-time';
      time.textContent = formatTime(it.remindAt) + (overdue ? ' (已超时)' : '');
      content.appendChild(time);
    }
    
    const actions = document.createElement('div');
    actions.className = 'item-actions';
    
    // 如果超时且未完成，显示重新设置时间按钮
    if (overdue) {
      const btnReschedule = document.createElement('button');
      btnReschedule.className = 'btn btn-reschedule';
      btnReschedule.textContent = '重设时间';
      btnReschedule.onclick = () => {
        const newTime = prompt('请选择新的提醒时间:', new Date(Date.now() + 60*60*1000).toISOString().slice(0,16));
        if (newTime) {
          const newTimestamp = Date.parse(newTime);
          if (!isNaN(newTimestamp)) {
            items[idx].remindAt = newTimestamp;
            saveItems(items);
            render(items);
          }
        }
      };
      actions.appendChild(btnReschedule);
    }
    
    const btnDone = document.createElement('button');
    btnDone.className = 'btn btn-secondary';
    btnDone.textContent = it.status === 'done' ? '撤销' : '完成';
    btnDone.style.padding = '6px 12px';
    btnDone.style.fontSize = '12px';
    
    const btnDel = document.createElement('button');
    btnDel.className = 'btn btn-danger';
    btnDel.textContent = '删除';
    btnDel.style.padding = '6px 12px';
    btnDel.style.fontSize = '12px';

    btnDone.onclick = () => {
      items[idx].status = it.status === 'done' ? 'todo' : 'done';
      saveItems(items);
      render(items);
    };
    
    btnDel.onclick = () => {
      items.splice(idx, 1);
      saveItems(items);
      render(items);
    };

    actions.appendChild(btnDone);
    actions.appendChild(btnDel);
    
    row.appendChild(status);
    row.appendChild(content);
    row.appendChild(actions);
    fragment.appendChild(row);
  });
  
  // 一次性插入所有元素，减少重排
  list.innerHTML = '';
  list.appendChild(fragment);
}

// 页面切换函数
function showListPage() {
  const mainPage = document.getElementById('mainPage');
  const listPage = document.getElementById('listPage');
  
  mainPage.classList.add('slide-out');
  setTimeout(() => {
    mainPage.classList.add('hidden');
    mainPage.classList.remove('slide-out');
    listPage.classList.remove('hidden');
  }, 150);
}

function showMainPage() {
  const mainPage = document.getElementById('mainPage');
  const listPage = document.getElementById('listPage');
  
  listPage.classList.add('hidden');
  setTimeout(() => {
    mainPage.classList.remove('hidden');
  }, 150);
}

// 全局暴露给 HTML onclick 使用
window.showListPage = showListPage;

window.addEventListener('DOMContentLoaded', () => {
  const local = (function() {
    try { const raw = localStorage.getItem(STORAGE_KEY); return raw ? JSON.parse(raw) : null; } catch { return null; }
  })();
  if (local && window.mylistAPI && window.mylistAPI.items) {
    // 迁移老数据至主进程
    window.mylistAPI.items.save(local);
    localStorage.removeItem(STORAGE_KEY);
  }

  let items = [];
  const bootstrap = async () => {
    if (window.mylistAPI && window.mylistAPI.items) {
      items = await window.mylistAPI.items.get();
    }
    renderPreview(items);
    renderFullList(items);
  };
  bootstrap();

  const input = document.getElementById('newItemInput');
  const addBtn = document.getElementById('addBtn');
  const clearDoneBtn = document.getElementById('clearDoneBtn');
  const timeInput = document.getElementById('timeInput');
  const btnClose = document.getElementById('btnClose');
  const btnClose2 = document.getElementById('btnClose2');
  const btnBack = document.getElementById('btnBack');

  addBtn.onclick = () => {
    const title = input.value.trim();
    if (!title) return;
    const remindAt = timeInput.value ? Date.parse(timeInput.value) : undefined;
    const item = { id: `${Date.now()}-${Math.random().toString(36).slice(2,8)}`, title, status: 'todo' };
    if (remindAt && !Number.isNaN(remindAt)) item.remindAt = remindAt;
    items.unshift(item);
    input.value = '';
    timeInput.value = '';
    saveItems(items);
    renderPreview(items);
    renderFullList(items);
  };

  input.addEventListener('keydown', (e) => {
    if (e.key === 'Enter') addBtn.click();
  });

  clearDoneBtn.onclick = () => {
    const left = items.filter((it) => it.status !== 'done');
    items.length = 0; items.push(...left);
    saveItems(items);
    renderPreview(items);
    renderFullList(items);
  };

  // 返回按钮
  btnBack.onclick = () => {
    showMainPage();
  };

  // 关闭按钮
  const closeApp = () => {
    if (window.mylistAPI && window.mylistAPI.togglePanel) {
      window.mylistAPI.togglePanel();
    } else {
      window.close();
    }
  };
  
  btnClose.onclick = closeApp;
  btnClose2.onclick = closeApp;
});


