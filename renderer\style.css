:root {
  --brand-red: #F22D40;
  --black: #000000;
  --ivory: #EFEEEC;
  --white: #FFFFFF;
  --black-05: rgba(0, 0, 0, 0.05);
  --black-10: rgba(0, 0, 0, 0.1);
  --black-15: rgba(0, 0, 0, 0.15);
  --black-30: rgba(0, 0, 0, 0.3);
  --black-50: rgba(0, 0, 0, 0.5);
  --black-80: rgba(0, 0, 0, 0.8);
  --black-20: rgba(0, 0, 0, 0.2);
  --black-40: rgba(0, 0, 0, 0.4);
  --red-10: rgba(242, 45, 64, 0.1);
  --red-20: rgba(242, 45, 64, 0.2);
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-heavy: 0 8px 32px rgba(0, 0, 0, 0.16);
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Inter, Helvetica, Arial, sans-serif;
  background: transparent;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
}

.panel {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 16px;
  border-radius: 16px;
  background: linear-gradient(135deg, var(--brand-red) 0%, #E91E63 100%);
  box-shadow: var(--shadow-heavy);
  -webkit-app-region: drag;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  color: var(--white);
  -webkit-app-region: no-drag;
}

.title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.02em;
}

.card {
  background: var(--white);
  border: none;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-light);
  transition: box-shadow 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
  contain: layout style paint;
}

.add-area:hover {
  box-shadow: var(--shadow-medium);
}

.add-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-row {
  display: flex;
  gap: 8px;
  align-items: stretch;
  flex-wrap: wrap;
}

@media (max-width: 400px) {
  .input-row {
    flex-direction: column;
  }
}

.input {
  flex: 1;
  min-width: 0;
  padding: 12px 16px;
  border: 1px solid var(--black-15);
  border-radius: 10px;
  font-size: 14px;
  font-family: inherit;
  background: var(--white);
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

#timeInput {
  flex: 0 0 auto;
  width: 180px;
}

@media (max-width: 400px) {
  #timeInput {
    width: 100%;
  }
}

.input:focus {
  outline: none;
  border-color: var(--brand-red);
  box-shadow: 0 0 0 3px var(--red-10);
}

.input::placeholder {
  color: var(--black-50);
}

.btn {
  padding: 12px 20px;
  border-radius: 10px;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  font-family: inherit;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.btn:hover {
  transform: translateZ(0) translateY(-1px);
  box-shadow: var(--shadow-medium);
}

.btn:active {
  transform: translateZ(0) translateY(0);
  transition: all 0.1s ease;
}

.btn-primary {
  background: var(--black);
  color: var(--white);
}

.btn-primary:hover {
  background: var(--black-80);
}

.btn-secondary {
  background: var(--black-05);
  color: var(--black-80);
}

.btn-secondary:hover {
  background: var(--black-10);
}

.btn-ghost {
  background: transparent;
  color: var(--white);
  padding: 8px 12px;
  font-size: 18px;
  font-weight: 400;
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: none;
  box-shadow: none;
}

.btn-danger {
  background: var(--brand-red);
  color: var(--white);
}

.btn-danger:hover {
  background: #d91e36;
}

.btn-reschedule {
  background: var(--brand-red);
  color: var(--white);
  font-size: 12px;
  padding: 6px 10px;
}

.btn-reschedule:hover {
  background: #d91e36;
}

.list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--black-20) transparent;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  padding-right: 2px;
}

.list::-webkit-scrollbar {
  width: 6px;
}

.list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 3px;
  margin: 4px 0;
}

.list::-webkit-scrollbar-thumb {
  background: var(--black-20);
  border-radius: 3px;
  transition: background 0.2s ease;
}

.list::-webkit-scrollbar-thumb:hover {
  background: var(--black-40);
}

.item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--white);
  border: 1px solid var(--black-05);
  border-radius: 10px;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
  transform: translateZ(0); /* 硬件加速 */
}

.item:hover {
  border-color: var(--black-15);
  box-shadow: var(--shadow-light);
  transform: translateZ(0) translateY(-1px);
}

.item.overdue {
  background: var(--black-05);
  opacity: 0.7;
  border-color: var(--black-10);
}

.item.overdue .item-title {
  color: var(--black-50);
}

.item.overdue .item-time {
  color: var(--black-30);
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-title {
  color: var(--black);
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
  margin: 0;
}

.item-title.done {
  color: var(--black-50);
  text-decoration: line-through;
}

.item-time {
  color: var(--black-50);
  font-size: 12px;
  font-weight: 400;
}

.item-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

.status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status.todo { background: var(--brand-red); }
.status.doing { background: var(--black); }
.status.done { background: var(--black-30); }

/* 页面切换样式 */
.page {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
  opacity: 1;
}

.page.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

.page.slide-out {
  transform: translateX(-100%);
  opacity: 0;
}

/* 预览区域样式 */
.list-preview {
  cursor: pointer;
  min-height: 140px;
  transition: all 0.2s ease;
  position: relative;
}

.list-preview:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--black-10);
}

.item-count {
  font-weight: 500;
  color: var(--black-80);
}

.expand-icon {
  font-size: 14px;
  color: var(--black-50);
  transition: transform 0.2s ease;
}

.list-preview:hover .expand-icon {
  transform: translateX(2px);
}

.preview-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 60px;
  overflow: hidden;
  margin-bottom: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 13px;
  opacity: 0.8;
}

.preview-item .status {
  width: 8px;
  height: 8px;
}

.preview-item.more {
  color: var(--black-50);
  font-style: italic;
  justify-content: center;
}

.click-hint {
  text-align: center;
  color: var(--black-50);
  font-size: 12px;
  padding: 8px 0;
  border-top: 1px solid var(--black-05);
  margin-top: auto;
}

/* 列表页面样式 */
.full-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  padding-right: 2px;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.list-footer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}


