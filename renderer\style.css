:root {
  --brand-red: #F22D40;
  --white: #FFFFFF;
  --text-primary: #1D1D1F;
  --text-secondary: rgba(29, 29, 31, 0.6);
  --text-tertiary: rgba(29, 29, 31, 0.3);

  /* 毛玻璃效果颜色 */
  --glass-red: rgba(242, 45, 64, 0.8);
  --glass-white: rgba(255, 255, 255, 0.7);
  --glass-white-light: rgba(255, 255, 255, 0.4);
  --glass-border: rgba(255, 255, 255, 0.2);

  /* 阴影 */
  --shadow-glass: 0 8px 32px rgba(242, 45, 64, 0.3);
  --shadow-card: 0 4px 20px rgba(0, 0, 0, 0.1);
  --shadow-button: 0 2px 10px rgba(0, 0, 0, 0.15);
}

* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', 'Se<PERSON>e UI', Inter, Helvetica, Arial, sans-serif;
  background: linear-gradient(135deg, #F22D40 0%, #FF6B7A 100%);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  backface-visibility: hidden;
  -webkit-backface-visibility: hidden;
  perspective: 1000px;
}

.panel {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 20px;
  border-radius: 20px;
  background: var(--glass-red);
  backdrop-filter: blur(20px) saturate(180%);
  -webkit-backdrop-filter: blur(20px) saturate(180%);
  border: 1px solid var(--glass-border);
  box-shadow: var(--shadow-glass);
  -webkit-app-region: drag;
  overflow: hidden;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
  color: var(--white);
  -webkit-app-region: no-drag;
}

.title {
  font-size: 22px;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.02em;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.card {
  background: var(--glass-white);
  backdrop-filter: blur(15px) saturate(180%);
  -webkit-backdrop-filter: blur(15px) saturate(180%);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 16px;
  box-shadow: var(--shadow-card);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
  contain: layout style paint;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.add-area {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.input-row {
  display: flex;
  gap: 8px;
  align-items: stretch;
  flex-wrap: wrap;
}

@media (max-width: 400px) {
  .input-row {
    flex-direction: column;
  }
}

.input {
  flex: 1;
  min-width: 0;
  padding: 14px 18px;
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  font-size: 15px;
  font-family: inherit;
  background: var(--glass-white-light);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
}

#timeInput {
  flex: 0 0 auto;
  width: 180px;
}

@media (max-width: 400px) {
  #timeInput {
    width: 100%;
  }
}

.input:focus {
  outline: none;
  border-color: var(--brand-red);
  background: var(--glass-white);
  box-shadow: 0 0 0 3px rgba(242, 45, 64, 0.2);
  transform: translateY(-1px);
}

.input::placeholder {
  color: var(--text-tertiary);
}

.btn {
  padding: 14px 24px;
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  cursor: pointer;
  font-size: 15px;
  font-weight: 600;
  font-family: inherit;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  -webkit-app-region: no-drag;
  position: relative;
  overflow: hidden;
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.btn:hover {
  transform: translateZ(0) translateY(-2px);
  box-shadow: var(--shadow-button);
  border-color: rgba(255, 255, 255, 0.3);
}

.btn:active {
  transform: translateZ(0) translateY(0);
  transition: all 0.1s ease;
}

.btn-primary {
  background: var(--brand-red);
  color: var(--white);
  border-color: var(--brand-red);
}

.btn-primary:hover {
  background: rgba(242, 45, 64, 0.9);
  box-shadow: 0 4px 20px rgba(242, 45, 64, 0.4);
}

.btn-secondary {
  background: var(--glass-white-light);
  color: var(--text-primary);
}

.btn-secondary:hover {
  background: var(--glass-white);
}

.btn-ghost {
  background: rgba(255, 255, 255, 0.1);
  color: var(--white);
  padding: 10px 14px;
  font-size: 18px;
  font-weight: 500;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.btn-ghost:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 2px 10px rgba(255, 255, 255, 0.1);
}

.btn-danger {
  background: rgba(242, 45, 64, 0.9);
  color: var(--white);
  border-color: var(--brand-red);
}

.btn-danger:hover {
  background: var(--brand-red);
  box-shadow: 0 4px 20px rgba(242, 45, 64, 0.5);
}

.list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-height: 320px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
  padding-right: 4px;
}

.list::-webkit-scrollbar {
  width: 8px;
}

.list::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  margin: 4px 0;
}

.list::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

.list::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

.item {
  display: flex;
  align-items: center;
  gap: 14px;
  padding: 16px 20px;
  background: var(--glass-white-light);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  border: 1px solid var(--glass-border);
  border-radius: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, box-shadow;
  transform: translateZ(0);
}

.item:hover {
  border-color: rgba(255, 255, 255, 0.4);
  background: var(--glass-white);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transform: translateZ(0) translateY(-2px);
}

.item.overdue {
  background: rgba(255, 255, 255, 0.2);
  opacity: 0.6;
  border-color: rgba(255, 255, 255, 0.1);
}

.item.overdue .item-title {
  color: var(--text-secondary);
}

.item.overdue .item-time {
  color: var(--text-tertiary);
}

.item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-title {
  color: var(--text-primary);
  font-size: 15px;
  font-weight: 600;
  line-height: 1.4;
  margin: 0;
}

.item-title.done {
  color: var(--text-secondary);
  text-decoration: line-through;
  opacity: 0.7;
}

.item-time {
  color: var(--text-secondary);
  font-size: 13px;
  font-weight: 500;
}

.item-actions {
  display: flex;
  gap: 8px;
  align-items: center;
  flex-shrink: 0;
}

.status {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.2s ease;
}

.status.todo {
  background: var(--brand-red);
  box-shadow: 0 0 8px rgba(242, 45, 64, 0.4);
}
.status.done {
  background: var(--white);
  border-color: var(--text-secondary);
}

/* 页面切换样式 */
.page {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform: translateX(0);
  opacity: 1;
}

.page.hidden {
  transform: translateX(100%);
  opacity: 0;
  pointer-events: none;
}

.page.slide-out {
  transform: translateX(-100%);
  opacity: 0;
}

/* 预览区域样式 */
.list-preview {
  cursor: pointer;
  min-height: 140px;
  transition: all 0.2s ease;
  position: relative;
}

.list-preview:hover {
  box-shadow: var(--shadow-medium);
  transform: translateY(-1px);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--black-10);
}

.item-count {
  font-weight: 500;
  color: var(--black-80);
}

.expand-icon {
  font-size: 14px;
  color: var(--black-50);
  transition: transform 0.2s ease;
}

.list-preview:hover .expand-icon {
  transform: translateX(2px);
}

.preview-items {
  display: flex;
  flex-direction: column;
  gap: 6px;
  max-height: 60px;
  overflow: hidden;
  margin-bottom: 12px;
}

.preview-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 0;
  font-size: 13px;
  opacity: 0.8;
}

.preview-item .status {
  width: 8px;
  height: 8px;
}

.preview-item.more {
  color: var(--black-50);
  font-style: italic;
  justify-content: center;
}

.click-hint {
  text-align: center;
  color: var(--black-50);
  font-size: 12px;
  padding: 8px 0;
  border-top: 1px solid var(--black-05);
  margin-top: auto;
}

/* 列表页面样式 */
.full-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  overflow-y: auto;
  padding-right: 2px;
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

.list-footer {
  margin-top: 16px;
  display: flex;
  justify-content: flex-end;
}


